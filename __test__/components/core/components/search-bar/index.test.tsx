import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import SearchBar from '@/components/core/components/search-bar';

describe('SearchBar', () => {
  it('renders basic search bar correctly', () => {
    render(<SearchBar placeholder="请输入搜索内容" />);
    const input = screen.getByPlaceholderText('请输入搜索内容');
    expect(input).toBeInTheDocument();
    expect(input.tagName).toBe('INPUT');
  });

  it('renders with search icon by default', () => {
    const { container } = render(<SearchBar />);
    const searchIcon = container.querySelector('svg');
    expect(searchIcon).toBeInTheDocument();
  });

  it('hides search icon when showSearchIcon is false', () => {
    const { container } = render(<SearchBar showSearchIcon={false} />);
    const searchIcon = container.querySelector('svg');
    expect(searchIcon).toBeNull();
  });

  it('handles controlled value correctly', () => {
    const { rerender } = render(<SearchBar value="test" />);
    const input = screen.getByDisplayValue('test');
    expect(input).toBeInTheDocument();

    rerender(<SearchBar value="updated" />);
    expect(screen.getByDisplayValue('updated')).toBeInTheDocument();
  });

  it('handles uncontrolled value correctly', () => {
    render(<SearchBar defaultValue="default" />);
    const input = screen.getByDisplayValue('default');
    expect(input).toBeInTheDocument();
  });

  it('calls onChange when input value changes', () => {
    const handleChange = vi.fn();
    render(<SearchBar onChange={handleChange} />);
    const input = screen.getByRole('textbox');

    fireEvent.change(input, { target: { value: 'test input' } });
    expect(handleChange).toHaveBeenCalledWith('test input');
  });

  it('calls onSearch when Enter key is pressed', () => {
    const handleSearch = vi.fn();
    render(<SearchBar onSearch={handleSearch} defaultValue="search term" />);
    const input = screen.getByRole('textbox');

    fireEvent.keyDown(input, { key: 'Enter' });
    expect(handleSearch).toHaveBeenCalledWith('search term');
  });

  it('shows clear button when there is input value', () => {
    const { container } = render(<SearchBar defaultValue="test" />);
    const clearButton = container.querySelector('svg:last-child');
    expect(clearButton).toBeInTheDocument();
  });

  it('hides clear button when input is empty', () => {
    const { container } = render(<SearchBar />);
    // 只有搜索图标，没有清除按钮
    const svgElements = container.querySelectorAll('svg');
    expect(svgElements).toHaveLength(1);
  });

  it('clears input when clear button is clicked', () => {
    const handleChange = vi.fn();
    const handleClear = vi.fn();
    const { container } = render(
      <SearchBar 
        defaultValue="test" 
        onChange={handleChange} 
        onClear={handleClear} 
      />
    );
    
    const clearButton = container.querySelector('svg:last-child');
    expect(clearButton).toBeInTheDocument();
    
    fireEvent.click(clearButton!);
    expect(handleChange).toHaveBeenCalledWith('');
    expect(handleClear).toHaveBeenCalled();
  });

  it('renders cancel button when showCancelButton is true', () => {
    render(<SearchBar showCancelButton cancelText="取消" />);
    const cancelButton = screen.getByText('取消');
    expect(cancelButton).toBeInTheDocument();
  });

  it('calls onCancel when cancel button is clicked', () => {
    const handleCancel = vi.fn();
    render(
      <SearchBar 
        showCancelButton 
        cancelText="取消" 
        onCancel={handleCancel} 
      />
    );
    
    const cancelButton = screen.getByText('取消');
    fireEvent.click(cancelButton);
    expect(handleCancel).toHaveBeenCalled();
  });

  it('handles disabled state correctly', () => {
    const { container } = render(
      <SearchBar disabled defaultValue="test" />
    );
    const input = screen.getByRole('textbox');
    expect(input).toBeDisabled();
    
    // 禁用状态下不显示清除按钮
    const clearButton = container.querySelector('svg:last-child');
    expect(clearButton).toBeNull();
  });

  it('handles readOnly state correctly', () => {
    const { container } = render(
      <SearchBar readOnly defaultValue="test" />
    );
    const input = screen.getByRole('textbox');
    expect(input).toHaveAttribute('readonly');
    
    // 只读状态下不显示清除按钮
    const clearButton = container.querySelector('svg:last-child');
    expect(clearButton).toBeNull();
  });

  it('respects maxLength prop', () => {
    render(<SearchBar maxLength={10} />);
    const input = screen.getByRole('textbox');
    expect(input).toHaveAttribute('maxLength', '10');
  });

  it('calls onFocus and onBlur correctly', () => {
    const handleFocus = vi.fn();
    const handleBlur = vi.fn();
    render(
      <SearchBar onFocus={handleFocus} onBlur={handleBlur} />
    );
    const input = screen.getByRole('textbox');

    fireEvent.focus(input);
    expect(handleFocus).toHaveBeenCalled();

    fireEvent.blur(input);
    expect(handleBlur).toHaveBeenCalled();
  });

  it('applies custom className', () => {
    const { container } = render(
      <SearchBar className="custom-search-bar" />
    );
    const wrapper = container.firstChild;
    expect(wrapper).toHaveClass('custom-search-bar');
  });

  it('hides clear button when showClearButton is false', () => {
    const { container } = render(
      <SearchBar showClearButton={false} defaultValue="test" />
    );
    // 只有搜索图标，没有清除按钮
    const svgElements = container.querySelectorAll('svg');
    expect(svgElements).toHaveLength(1);
  });
});
