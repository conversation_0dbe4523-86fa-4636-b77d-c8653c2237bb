# SearchBar 搜索框

基于Input组件实现的搜索框组件，类似antd-mobile的SearchBar设计，支持搜索图标、清除按钮和取消按钮。

## 代码演示

### 基础用法

```tsx
import SearchBar from '@/components/core/components/search-bar';

export default () => (
  <SearchBar placeholder="请输入搜索内容" />
);
```

### 受控组件

```tsx
import { useState } from 'react';
import SearchBar from '@/components/core/components/search-bar';

export default () => {
  const [value, setValue] = useState('');
  
  return (
    <SearchBar 
      value={value} 
      onChange={setValue} 
      onSearch={(val) => console.log('搜索:', val)}
      placeholder="请输入搜索内容"
    />
  );
};
```

### 带取消按钮

```tsx
import SearchBar from '@/components/core/components/search-bar';

export default () => (
  <SearchBar 
    showCancelButton
    cancelText="取消"
    onCancel={() => console.log('取消搜索')}
    placeholder="请输入搜索内容"
  />
);
```

### 自定义配置

```tsx
import SearchBar from '@/components/core/components/search-bar';

export default () => (
  <>
    <SearchBar 
      showSearchIcon={false}
      placeholder="不显示搜索图标"
    />
    <SearchBar 
      showClearButton={false}
      placeholder="不显示清除按钮"
    />
    <SearchBar 
      maxLength={20}
      placeholder="最多输入20个字符"
    />
  </>
);
```

### 禁用和只读状态

```tsx
import SearchBar from '@/components/core/components/search-bar';

export default () => (
  <>
    <SearchBar 
      disabled
      defaultValue="禁用状态"
      placeholder="请输入搜索内容"
    />
    <SearchBar 
      readOnly
      defaultValue="只读状态"
      placeholder="请输入搜索内容"
    />
  </>
);
```

### 事件处理

```tsx
import SearchBar from '@/components/core/components/search-bar';

export default () => (
  <SearchBar 
    placeholder="请输入搜索内容"
    onChange={(value) => console.log('输入变化:', value)}
    onSearch={(value) => console.log('搜索:', value)}
    onClear={() => console.log('清除内容')}
    onFocus={() => console.log('获得焦点')}
    onBlur={() => console.log('失去焦点')}
  />
);
```

## API

### 属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| `value` | 搜索框的值 | `string` | - |
| `defaultValue` | 搜索框默认值 | `string` | - |
| `placeholder` | 占位符文本 | `string` | `'请输入搜索内容'` |
| `showSearchIcon` | 是否显示搜索图标 | `boolean` | `true` |
| `showClearButton` | 是否显示清除按钮 | `boolean` | `true` |
| `showCancelButton` | 是否显示取消按钮 | `boolean` | `false` |
| `cancelText` | 取消按钮文本 | `string` | `'取消'` |
| `disabled` | 是否禁用 | `boolean` | `false` |
| `readOnly` | 是否只读 | `boolean` | `false` |
| `maxLength` | 最大输入长度 | `number` | - |
| `className` | 自定义类名 | `string` | - |

### 事件

| 事件名 | 说明 | 类型 |
| --- | --- | --- |
| `onChange` | 值变化时的回调函数 | `(value: string) => void` |
| `onSearch` | 搜索时的回调函数（按Enter键触发） | `(value: string) => void` |
| `onClear` | 清除时的回调函数 | `() => void` |
| `onCancel` | 取消时的回调函数 | `() => void` |
| `onFocus` | 获得焦点时的回调函数 | `() => void` |
| `onBlur` | 失去焦点时的回调函数 | `() => void` |

## 样式定制

SearchBar 使用 Tailwind CSS 进行样式设置，支持以下预设样式：

- 容器：白色背景，灰色边框，聚焦时蓝色边框
- 搜索图标：左侧灰色图标
- 清除按钮：右侧可点击的清除图标
- 取消按钮：蓝色文字按钮
- 输入框：透明背景，无边框

可以通过 `className` 属性进行样式覆盖：

```tsx
<SearchBar className="border-purple-500" />
```

## 交互行为

1. **搜索触发**：按Enter键或调用onSearch回调
2. **清除功能**：点击清除按钮清空输入内容并重新聚焦
3. **取消功能**：点击取消按钮触发onCancel回调
4. **自动显示/隐藏**：清除按钮仅在有内容且非禁用/只读状态时显示

## 无障碍

- 支持键盘操作（Enter键搜索）
- 适当的焦点管理
- 语义化的HTML结构

## 注意事项

1. 当组件为受控组件时（提供了 `value` 属性），内部状态会跟随外部 `value` 变化
2. 清除按钮只在有输入内容且非禁用/只读状态时显示
3. 搜索功能通过按Enter键或调用onSearch回调触发
4. 组件基于现有的Input组件设计模式，保持一致的API风格
