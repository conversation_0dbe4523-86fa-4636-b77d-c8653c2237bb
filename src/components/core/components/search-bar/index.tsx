import type { KeyboardEvent, Ref } from 'react';
import { useEffect, useRef, useState } from 'react';
import { cn } from '@/components/core/class-config';
import classConfig from '@/components/core/components/search-bar/class-config.ts';
import { ClearIcon, SearchIcon } from '@/components/core/components/search-bar/icons.tsx';

export type SearchBarProps = {
  /** 搜索框的值 */
  value?: string;
  /** 搜索框默认值 */
  defaultValue?: string;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否显示搜索图标 */
  showSearchIcon?: boolean;
  /** 是否显示清除按钮 */
  showClearButton?: boolean;
  /** 是否显示取消按钮 */
  showCancelButton?: boolean;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否只读 */
  readOnly?: boolean;
  /** 最大输入长度 */
  maxLength?: number;
  /** 自定义类名 */
  className?: string;
  /** 值变化时的回调函数 */
  onChange?: (value: string) => void;
  /** 搜索时的回调函数 */
  onSearch?: (value: string) => void;
  /** 清除时的回调函数 */
  onClear?: () => void;
  /** 取消时的回调函数 */
  onCancel?: () => void;
  /** 获得焦点时的回调函数 */
  onFocus?: () => void;
  /** 失去焦点时的回调函数 */
  onBlur?: () => void;
  /** ref引用 */
  ref?: Ref<HTMLInputElement>;
};

const SearchBar = (props: SearchBarProps) => {
  const {
    value,
    defaultValue,
    placeholder = '请输入搜索内容',
    showSearchIcon = true,
    showClearButton = true,
    showCancelButton = false,
    cancelText = '取消',
    disabled = false,
    readOnly = false,
    maxLength,
    className,
    onChange,
    onSearch,
    onClear,
    onCancel,
    onFocus,
    onBlur,
    ref,
  } = props;

  const [innerValue, setInnerValue] = useState(defaultValue || value || '');
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (value !== undefined) {
      setInnerValue(value);
    }
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    if (value === undefined) {
      setInnerValue(newValue);
    }
    onChange?.(newValue);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearch?.(innerValue);
    }
  };

  const handleClear = () => {
    const newValue = '';
    if (value === undefined) {
      setInnerValue(newValue);
    }
    onChange?.(newValue);
    onClear?.();
    inputRef.current?.focus();
  };

  const handleCancel = () => {
    onCancel?.();
  };

  const handleFocus = () => {
    onFocus?.();
  };

  const handleBlur = () => {
    onBlur?.();
  };

  const inputClassName = cn(
    classConfig.inputConfig,
    showSearchIcon && classConfig.inputWithSearchIconConfig,
    showClearButton && innerValue && classConfig.inputWithClearButtonConfig,
  );

  return (
    <div className={cn('flex items-center', className)}>
      <div className={cn(classConfig.containerConfig)}>
        {showSearchIcon && (
          <SearchIcon className={cn(classConfig.searchIconConfig)} />
        )}
        
        <input
          ref={ref || inputRef}
          type="text"
          value={innerValue}
          placeholder={placeholder}
          disabled={disabled}
          readOnly={readOnly}
          maxLength={maxLength}
          className={inputClassName}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
        />
        
        {showClearButton && innerValue && !disabled && !readOnly && (
          <ClearIcon
            className={cn(classConfig.clearButtonConfig)}
            onClick={handleClear}
          />
        )}
      </div>
      
      {showCancelButton && (
        <div
          className={cn(classConfig.cancelButtonConfig)}
          onClick={handleCancel}
        >
          {cancelText}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
