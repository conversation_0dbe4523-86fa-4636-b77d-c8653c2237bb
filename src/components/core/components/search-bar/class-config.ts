import { getComponentClassConfig } from '@/components/core/class-config';
import type { defaultConfig } from '@/components/core/class-config/default-config.ts';

const currentConfig = getComponentClassConfig(
  'searchBar',
) as (typeof defaultConfig)['searchBar'];

const classConfig = {
  containerConfig: currentConfig.container,
  inputConfig: currentConfig.input,
  searchIconConfig: currentConfig.searchIcon,
  clearButtonConfig: currentConfig.clearButton,
  cancelButtonConfig: currentConfig.cancelButton,
  inputWithSearchIconConfig: currentConfig.inputWithSearchIcon,
  inputWithClearButtonConfig: currentConfig.inputWithClearButton,
};

export default classConfig;
